<script setup lang="ts" name="Demo">
import { ref, onMounted, watch } from "vue";
import { Loading, showNotify, showDialog } from "vant";
import "vant/es/notify/style";
import "vant/es/dialog/style";
import { ArmcloudEngine } from "armcloud-rtc";

const armCloud: any = ref(null);

// 加入房间标识
const joinRoomFlag = ref(false);
// 旋转方向
const rotateType = ref(0);

const showLeftFlag = ref(false);

// 操作按钮
function handleOperation() {
  showLeftFlag.value = true;
}

/** 唤起房间初始化配置弹窗 */
const roomConfigFlag = ref(false);
const roomConfig = ref({
  baseUrl: "",
  activateGroupControl: 0,
  pads: "",
  // 用户id
  userId: "",
  token: "",
  padCode: "",
  expire: 3600,
  // 清晰度
  videoStream: {
    resolution: 13,
    frameRate: 2,
    bitrate: 3,
  },
  uuid: "",
  // 无操作回收时间(秒)
  autoRecoveryTime: 300,

  // 渲染模式 0：自适应屏幕 1：填充屏幕
  isFullScreen: 0,

  // 媒体流类型，1：只控制音频 2：只控制视频 3：同时控制音频和视频
  mediaType: 3,

  // 旋转屏幕 type 1：横屏；0：竖屏
  rotateType: 0,

  // 设置清晰度 1：标清 2：高清 3：超清
  definition: 2,

  // 是否启用本地输入法 'local': 本机键盘，'pad': 云机键盘
  keyboard: "pad",

  // 允许云机复制到真机
  allowCopyRemoteToLocal: true,
});

if (localStorage.getItem("armcloud_uuid")) {
  roomConfig.value.uuid = localStorage.getItem("armcloud_uuid");
}
if (localStorage.getItem("armcloud_token")) {
  roomConfig.value.token = localStorage.getItem("armcloud_token");
}

if (localStorage.getItem("padCode")) {
  roomConfig.value.padCode = localStorage.getItem("padCode");
}
if (localStorage.getItem("userId")) {
  roomConfig.value.userId = localStorage.getItem("userId");
}

watch(
  () => roomConfig.value.uuid,
  (newValue) => {
    newValue && localStorage.setItem("armcloud_uuid", newValue);
  }
);

watch(
  () => roomConfig.value.token,
  (newValue) => {
    newValue && localStorage.setItem("armcloud_token", newValue);
  }
);

watch(
  () => roomConfig.value.padCode,
  (newValue) => {
    newValue && localStorage.setItem("padCode", newValue);
  }
);

watch(
  () => roomConfig.value.userId,
  (newValue) => {
    newValue && localStorage.setItem("userId", newValue);
  }
);

// 视频传输网络延迟
const videoDelay = ref<any>(null);
function handleOpenRoomConfig() {
  roomConfigFlag.value = true;
}
const isLoading = ref(false);

async function handleConfirmJoinRoom() {
  isLoading.value = true;

  if (armCloud.value) {
    armCloud.value = null;
  }

  // RTC初始化
  const paramsObj = {
    token: roomConfig.value.token,
    baseUrl: "https://openapi-hk.armcloud.net",
    isWsProxy: false,
    retryCount: 5,
    retryTime: 1000,
    enableMicrophone: enableMicrophone.value,
    enableCamera: enableCamera.value,
    deviceInfo: {
      padCode: roomConfig.value.padCode,
      userId: roomConfig.value.userId,
      videoStream: {
        resolution: 13, // 分辨率
        frameRate: 2, // 帧率
        bitrate: 3, // 码率
      },
      autoRecoveryTime: 0,
      isFullScreen: roomConfig.value.isFullScreen,
      mediaType: roomConfig.value.mediaType,
      definition: roomConfig.value.definition,
      keyboard: roomConfig.value.keyboard,
      saveCloudClipboard: roomConfig.value.allowCopyRemoteToLocal,
      allowLocalIMEInCloud: true,
      disableLocalIME: true,
    },
    viewId: "phoneBox",
    callbacks: {
      // 初始化回调
      onInit: async (res) => {
        console.log("init:", res);
        // 加入房间
        armCloud.value.start(
          !!roomConfig.value.activateGroupControl,
          roomConfig.value.pads?.split(",")
        );
        showLeftFlag.value = false;
      },
      // 链接成功回调
      onConnectSuccess: () => {
        showLeftFlag.value = false;
        joinRoomFlag.value = true;
        showNotify({
          type: "success",
          message: "进入房间成功",
        });
        // 获取SDK版本
        const version = armCloud.value.version;
        console.log("SDK version:", version);
      },
      // 监控操作信息
      onMonitorOperation: (data) => {
        console.log("监控操作信息", data);
      },
      // 链接失败回调
      onConnectFail: ({ code, msg }) => {
        console.log("fail:", code, msg);
        showNotify({
          type: "danger",
          message: msg,
        });
      },
      // 打开或者关闭(摄像头/麦克风)回调
      onMediaDevicesToggle: (data) => {
        console.log("打开或者关闭(摄像头/麦克风)", data);
      }, // 无操作回收回调
      onAutoRecoveryTime: () => {
        console.log("触发回收回调");
        showDialog({
          message: "触发无操作回收，暂停拉流; 点击确认，恢复拉流",
        }).then(() => {
          // on close
          // armCloud.value.resumeAllSubscribedStream(3);
          handleConfirmJoinRoom();
        });
      },
      // 自动播放失败回调
      onAutoplayFailed: (e) => {
        console.log("自动播放失败", e);
        if (e.kind === "video") {
          showDialog({
            message: "自动播放视频失败; 点击确认，手动播放",
          }).then(() => {
            // on close
            armCloud.value.startPlay();
          });
        }
        if (e.kind === "audio") {
          showDialog({
            message: "自动播放音频失败; 点击确认，手动播放",
          }).then(() => {
            // on close
            armCloud.value.startPlay();
          });
        }
      },
      // 当前运行信息回调
      onRunInformation: (info) => {
        videoDelay.value = info?.videoStats?.rtt ?? null;
      },
      // 当分辨率发生变化时触发
      onChangeResolution: (width, height) => {
        console.log("当前大小", width, height);
      },
      // 当收到云端app透传的消息时的触发
      onTransparentMsg: (type, msg) => {
        console.log("消息透传", type, msg);
      },
      // 当播放出现异常时触发
      onErrorMessage: (event) => {
        console.log("异常", event);
      },
      // 云机消息复制回调
      onOutputClipper: (message) => {
        console.log("剪切板回调", message);
      },
      // 视频首帧渲染成功
      onRenderedFirstFrame: (event) => {
        console.log("视频首帧渲染成功", event);
        armCloud.value.getEquipmentInfo("app");
        armCloud.value.getEquipmentInfo("attr");

        isLoading.value = false;
      },

      // 恢复音频成功后
      onAudioRecovery: () => {
        console.log("onAudioRecovery...");
      },
      // 暂停音频成功后
      onAudioPause: () => {
        console.log("onAudioPause...");
      },
      onSocketCallback({ code }) {
        console.log(code);
      },
      onProgress(info) {
        console.log(info);
      },
      onGroupControlError(info) {
        console.log(info);
      },
      onUserLeaveOrJoin(info) {
        console.log(info);
      },
      onSendUserError(info) {
        console.log("onSendUserError", info.code);
      },
      onConnectionStateChanged(info) {
        console.log("onConnectionStateChanged", info);
      },
      onEquipmentInfo(info) {},
      onVideoInit(err) {
        console.log("onVideoInit", err);
      },
      onVideoError(err) {
        console.log("onVideoInit", err.code, err);
      },
      // 音频采集成功
      onAudioInit(err) {
        console.log("onAudioInit", err);
      },
      // 音频采集失败
      onAudioError(err) {
        console.log("onAudioError", err);
      },
      onInjectVideoResult(type, data) {
        console.log("onInjectVideoResult", type, data);
      },
      onChangeRotate(type, data) {
        console.log("onChangeRotate", type, data);
      },
      onUserJoined(user) {
        console.log("onUserJoined", user);
      },
    },
  };

  armCloud.value = new ArmcloudEngine(paramsObj);
  window.engine = armCloud.value;
}

const handleShakeIt = () => {
  armCloud.value.sendShake();
};
/** 离开房间 */
function handleLeaveRoom() {
  armCloud.value
    .stop()
    .then((res) => {
      console.log(res);
    })
    .catch((err) => {
      console.log(err);
    });
  isLoading.value = false;
  showLeftFlag.value = false;
  joinRoomFlag.value = false;
  // handleConfirmJoinRoom();
}

// 自定义清晰度
const definitionFlag = ref(false);
const definitionConfig = ref({
  definitionId: 13,
  framerateId: 2,
  bitrateId: 3,
});
function handleOpenSwitchVideoStream() {
  definitionFlag.value = true;
}
function handleConfirmVideoStream() {
  armCloud.value.setStreamConfig(definitionConfig.value);

  definitionFlag.value = false;
  showLeftFlag.value = false;
}
/** 暂停/恢复接收来自远端的媒体流（音视频） */
function handleSetAudioMute(mute: boolean) {
  // armCloud.value.setAudioMute(mute)
  if (mute) armCloud.value.resumeAllSubscribedStream(1);
  else armCloud.value.pauseAllSubscribedStream(1);

  showLeftFlag.value = false;
}
// 截图
const snapshotFlag = ref(false);
const snapshotSaveType = ref("1");
function handleOpenSnapshot() {
  snapshotFlag.value = true;
}
function handleConfirmSaveSnapshot() {
  if (snapshotSaveType.value === "1") {
    // 保存到本地
    armCloud.value.saveScreenShotToLocal().then((res) => {
      console.log(111, res);
      const imageData = res;
      // 创建 canvas 元素
      const canvas: HTMLCanvasElement = document.createElement("canvas");
      canvas.width = imageData.width;
      canvas.height = imageData.height;
      const ctx = canvas.getContext("2d");

      // 将图像数据绘制到 canvas 上
      ctx.putImageData(imageData, 0, 0);
      // 将Canvas转换为图片并保存到本地
      const link = document.createElement("a");
      // 使用Canvas的toDataURL()方法将Canvas转换为base64编码的图片URL
      link.href = canvas.toDataURL();
      // 设置下载的文件名
      link.download = "image.png";
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    });
  } else {
    // 保存到云机
    armCloud.value.saveScreenShotToRemote();
  }
  showLeftFlag.value = false;
}

// 旋转屏幕 type 1：横屏；0：竖屏
function handleRotateScreen(degree: number) {
  if (degree == 90) {
    armCloud.value.setPhoneRotation(1);
  } else {
    armCloud.value.setPhoneRotation(0);
  }
  // armCloud.value.rotateScreen(degree);
  rotateType.value = degree === 90 ? 1 : 0;
  showLeftFlag.value = false;
}

// 开启自动定位
function handleOpenAutoPosition() {
  if (navigator.geolocation) {
    navigator.geolocation.getCurrentPosition((res) => {
      console.log("自动定位信息", res.coords);
      const options = {
        accuracy: res.coords.accuracy,
        altitude: res.coords.altitude,
        latitude: res.coords.latitude,
        longitude: res.coords.longitude,
        speed: res.coords.speed,
      };
      armCloud.value.setAutomaticPosition(options);
      showNotify({
        type: "success",
        message: "当前使用自动定位",
      });
      showLeftFlag.value = false;
    });
  } else {
    showNotify({
      type: "warning",
      message: "当前浏览器不支持自动定位",
    });
  }
}

// 手动传入定位
const lonLatFlag = ref(false);
const longitude = ref<number | null>(null);
const latitude = ref<number | null>(null);
function handleOpenCustomPosition() {
  lonLatFlag.value = true;
}
function handleConfirmLonLat() {
  armCloud.value.setGPS(longitude.value, latitude.value);
  showLeftFlag.value = false;
}

// 使用手机本地输入法
function handleUseLocalKeyboard() {
  if (roomConfig.value.keyboard === "local") {
    roomConfig.value.keyboard = "pad";
  } else {
    roomConfig.value.keyboard = "local";
  }
  console.log(roomConfig.value.keyboard);
  armCloud.value.setKeyboardStyle(roomConfig.value.keyboard);
  showLeftFlag.value = false;
}

/** 陀螺仪 */
// function handleDeviceorientation() {
//   if (window.DeviceMotionEvent) {
//     window.addEventListener(
//       "deviceorientation",
//       function (e) {
//         console.log(222, e);
//       },
//       false
//     );
//   } else {
//     alert("您的手机不支持陀螺仪哦~");
//   }
// }

// 能否使用震动
function vibratePhone() {
  // 检查浏览器是否支持Vibration API
  if ("vibrate" in navigator) {
    // 触发手机震动，参数为一个震动模式数组
    // 在本例中，[100, 100, 100]表示手机会震动3次，每次持续100毫秒
    navigator.vibrate([100]);
  } else {
    // 浏览器不支持Vibration API，给出提示
    alert("您的浏览器不支持震动功能");
  }
}

// 设置无操作过期时间
const recoveryTimeFlag = ref(false);
const expirationTime = ref(300);
function handleSetRecoveryTime() {
  recoveryTimeFlag.value = true;
}
function handleConfirmRecoveryTime() {
  armCloud.value.setAutoRecycleTime(expirationTime.value);
  recoveryTimeFlag.value = false;
  showLeftFlag.value = false;
}

/** 获取无操作过期时间 */
function handleGetRecoveryTime() {
  const time = armCloud.value.getAutoRecycleTime();
  showDialog({
    message: `无操作过期时间: ${time}`,
  });
  expirationTime.value = time;
  showLeftFlag.value = false;
}

/** 返回上一页 */
function handleGoBack() {
  // armCloud.value.goAppUpPage();
  armCloud.value.sendCommand("back");
}
/** 返回主页 */
function handleGoHome() {
  // armCloud.value.goAppHome();
  armCloud.value.sendCommand("home");
}
/** 返回任务栏 */
function handleGoTaskbar() {
  armCloud.value.sendCommand("menu");
}
/** 菜单 */
// function handleGoAppMenu() {
//   armCloud.value.goAppMenu();
// }
/** 音量+ */
function handleIncreaseVolume() {
  armCloud.value.increaseVolume();
}
/** 音量- */
function handleDecreaseVolume() {
  armCloud.value.decreaseVolume();
}
/** 允许真机剪切板复制到云机 */
const clipperFlag = ref(false);
const clipperConetent = ref("");
function handleAllowCopyLocalToRemote() {
  clipperFlag.value = true;
}
function handleConfirmClipper() {
  // armCloud.value.sendInputClipper(clipperConetent.value);
  armCloud.value.sendInputString(clipperConetent.value);
}

function handleAllowCopyRemoteToLocal() {
  roomConfig.value.allowCopyRemoteToLocal =
    !roomConfig.value.allowCopyRemoteToLocal;
  armCloud.value.saveCloudClipboard(roomConfig.value.allowCopyRemoteToLocal);
}

const pageAvailHeight = `${document.body.clientHeight - 40}px`;
onMounted(() => {
  // 监听页面刷新
  window.onbeforeunload = () => {
    console.log("refresh");
    if (armCloud.value) {
      armCloud.value.leaveRoom();
    }
  };
});

/** 操作按钮拖拽 */
const operateRef = ref(null);
const operateBtnTop = ref(400);
const operateBtnLeft = ref(320);
function handleDrag(e) {
  e.preventDefault();
  let touch = e.targetTouches[0];
  operateBtnTop.value = touch.clientY - 50;
  operateBtnLeft.value = touch.clientX - 50;
}
const enableMicrophone = ref<any>(true);
const enableCamera = ref<any>(true);
const turnOnoffMicrophone = () => {
  enableMicrophone.value = !enableMicrophone.value;
  armCloud.value?.setMicrophone(enableMicrophone.value);
};
const switchCameraOnoff = () => {
  enableCamera.value = !enableCamera.value;
  armCloud.value?.setCamera(enableCamera.value);
};
</script>

<template>
  <div class="video-container">
    <div
      class="loading-container"
      :style="{ height: pageAvailHeight }"
      v-if="isLoading"
    >
      <van-loading type="spinner" color="#1989fa" />
    </div>
    <div id="phoneBox" class="video-box" :style="{ height: pageAvailHeight }" />
    <div class="bottom-btn">
      <div @click="handleGoBack">返回</div>
      <div @click="handleGoHome">主页</div>
      <div @click="handleGoTaskbar">任务栏</div>
      <!-- <div @click="handleGoAppMenu">菜单</div> -->
      <div>视频延迟：{{ videoDelay }}</div>
    </div>
  </div>

  <!-- 悬浮操作按钮 -->
  <div
    ref="operateRef"
    class="operate-btn"
    :style="{ top: operateBtnTop + 'px', left: operateBtnLeft + 'px' }"
    @click="handleOperation"
    @touchmove="handleDrag"
  >
    操作
  </div>

  <van-popup
    v-model:show="showLeftFlag"
    position="left"
    :style="{ width: '80%', height: '100%' }"
  >
    <div class="flex flex-col">
      <van-button type="primary" @click="handleOpenRoomConfig">
        加入房间
      </van-button>
      <van-button type="primary" @click="handleLeaveRoom">
        退出房间
      </van-button>
      <van-button type="primary" @click="turnOnoffMicrophone">
        {{ enableMicrophone ? "关闭麦克风" : "开启麦克风" }}
      </van-button>
      <van-button type="primary" @click="switchCameraOnoff">
        {{ enableCamera ? "关闭摄像头" : "开启摄像头" }}
      </van-button>
      <van-button type="primary" @click="handleShakeIt"> 摇一摇 </van-button>
      <van-button type="primary" @click="handleOpenSwitchVideoStream">
        自定义清晰度
      </van-button>
      <van-button type="primary" @click="handleSetAudioMute(false)">
        静音
      </van-button>
      <van-button type="primary" @click="handleSetAudioMute(true)">
        取消静音
      </van-button>
      <van-button type="primary" @click="handleOpenSnapshot"> 截图 </van-button>
      <van-button type="primary" @click="handleRotateScreen(90)">
        手动横屏
      </van-button>
      <van-button type="primary" @click="handleRotateScreen(0)">
        手动竖屏
      </van-button>
      <van-button type="primary" @click="handleOpenAutoPosition">
        开启自动定位
      </van-button>
      <van-button type="primary" @click="handleOpenCustomPosition">
        手动传入定位
      </van-button>
      <van-button type="primary" @click="handleUseLocalKeyboard">
        使用手机本地输入法：{{ roomConfig.keyboard }}
      </van-button>
      <!-- <van-button type="primary" @click="handleDeviceorientation">
        启用陀螺仪：{{ false }}
      </van-button> -->
      <van-button type="primary" @click="vibratePhone">
        能否使用震动：{{ false }}
      </van-button>
      <!-- <van-button type="primary">
        切换视频渲染模式
      </van-button> -->
      <van-button type="primary" @click="handleSetRecoveryTime">
        设置无操作回收时间
      </van-button>
      <van-button type="primary" @click="handleGetRecoveryTime">
        获取无操作回收时间
      </van-button>
      <van-button type="primary" @click="handleIncreaseVolume">
        音量+
      </van-button>
      <van-button type="primary" @click="handleDecreaseVolume">
        音量-
      </van-button>
      <van-button type="primary" @click="handleAllowCopyLocalToRemote">
        允许真机剪切板复制到云机
      </van-button>
      <van-button type="primary" @click="handleAllowCopyRemoteToLocal">
        是否接收云机剪切板回调：{{ roomConfig.allowCopyRemoteToLocal }}
      </van-button>
    </div>
  </van-popup>
  <!-- 加入房间初始化配置弹窗 -->
  <van-dialog
    v-model:show="roomConfigFlag"
    show-cancel-button
    @confirm="handleConfirmJoinRoom"
  >
    <van-cell-group inset>
      <van-field
        v-model="roomConfig.userId"
        label="用户id"
        label-width="50px"
        placeholder="请输入"
      />
      <van-field
        v-model="roomConfig.token"
        label="token"
        label-width="50px"
        placeholder="请输入"
      />
      <van-field
        v-model="roomConfig.uuid"
        label="uuid"
        label-width="50px"
        placeholder="请输入"
      />
      <van-field
        v-model="roomConfig.padCode"
        label="房间号"
        label-width="50px"
        placeholder="请输入"
      />

      <!-- <van-field
        v-model="roomConfig.uuid"
        label="uuid"
        label-width="50px"
        placeholder="请输入"
      /> -->

      <span>群控配置：</span>
      <van-radio-group
        v-model="roomConfig.activateGroupControl"
        shape="dot"
        direction="horizontal"
        class="mb-[12px]"
      >
        <van-radio :name="0"> 关闭 </van-radio>
        <van-radio :name="1"> 开启 </van-radio>
      </van-radio-group>
      <van-field
        v-model="roomConfig.pads"
        label="房间号"
        label-width="50px"
        placeholder="请输入 多个房间号逗号隔开"
      />
      <span>-------群控结束-----</span>

      <van-radio-group
        v-model="roomConfig.isFullScreen"
        shape="dot"
        direction="horizontal"
        class="mb-[12px]"
      >
        <van-radio :name="0"> 自适应屏幕 </van-radio>
        <van-radio :name="1"> 填充屏幕 </van-radio>
      </van-radio-group>
      <van-radio-group
        v-model="roomConfig.mediaType"
        shape="dot"
        class="mb-[12px] flex"
      >
        <van-radio :name="1" class="mr-[2px]"> 只拉音频 </van-radio>
        <van-radio :name="2" class="mr-[2px]"> 只拉视频 </van-radio>
        <van-radio :name="3"> 拉音视频 </van-radio>
        <van-radio :name="4"> 不拉音视频 </van-radio>
      </van-radio-group>
      <van-radio-group
        v-model="roomConfig.rotateType"
        shape="dot"
        direction="horizontal"
        class="mb-[12px]"
      >
        <van-radio :name="1"> 横屏 </van-radio>
        <van-radio :name="0"> 竖屏 </van-radio>
      </van-radio-group>
    </van-cell-group>
  </van-dialog>

  <!-- 自定义清晰度 -->
  <van-dialog
    v-model:show="definitionFlag"
    title="自定义清晰度"
    show-cancel-button
    @confirm="handleConfirmVideoStream"
  >
    <van-cell-group inset>
      <van-field
        v-model="definitionConfig.definitionId"
        type="digit"
        label="分辨率"
      />
      <van-field
        v-model="definitionConfig.framerateId"
        type="digit"
        label="帧率"
      />
      <van-field
        v-model="definitionConfig.bitrateId"
        type="digit"
        label="码率"
      />
    </van-cell-group>
  </van-dialog>

  <!-- 截图（保存到本地、云机） -->
  <van-dialog
    v-model:show="snapshotFlag"
    title="截图"
    show-cancel-button
    @confirm="handleConfirmSaveSnapshot"
  >
    <van-radio-group v-model="snapshotSaveType">
      <van-radio name="1" class="mb-10px"> 保存到本地 </van-radio>
      <van-radio name="2"> 保存到云机 </van-radio>
    </van-radio-group>
  </van-dialog>

  <!-- 手动定位 -->
  <van-dialog
    v-model:show="lonLatFlag"
    title="经纬度"
    show-cancel-button
    @confirm="handleConfirmLonLat"
  >
    <van-cell-group inset>
      <van-field v-model="longitude" type="number" label="经度" />
      <van-field v-model="latitude" type="number" label="纬度" />
    </van-cell-group>
  </van-dialog>

  <!-- 无操作过期时间 -->
  <van-dialog
    v-model:show="recoveryTimeFlag"
    @confirm="handleConfirmRecoveryTime"
  >
    <van-field
      v-model="expirationTime"
      type="digit"
      label="无操作过期时间"
      label-width="120"
    />
  </van-dialog>

  <!-- 发送真机剪切板内容 -->
  <van-dialog
    v-model:show="clipperFlag"
    title="剪切板"
    show-cancel-button
    @confirm="handleConfirmClipper"
  >
    <van-field v-model="clipperConetent" label="剪切板内容" />
  </van-dialog>
</template>

<style>
.operate-btn {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: lightblue;
  display: flex;
  justify-content: center;
  align-items: center;
  position: fixed;
  /* top: 400px;
  left: 20px; */
}
.video-container {
  /* width: 100vw; */
  /* height: 100vh; */
  background-color: #eee;
  display: flex;
  flex-direction: column;
}
.loading-container {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}
.video-box {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}
.bottom-btn {
  width: 100%;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: space-around;
  background: #fff;
  color: #000;
}
</style>
