{"name": "armcloud-rtc", "version": "1.5.0", "main": "./src/index.ts", "module": "./src/index.ts", "types": "", "files": ["dist"], "keywords": ["rtc", "armcloud-rtc", "webrtc"], "scripts": {"build": "rimraf dist && rollup --config rollup.config.ts --configPlugin typescript"}, "author": "ask", "license": "ISC", "description": "armcloud-rtc sdk", "dependencies": {"@volcengine/rtc": "^4.61.2", "axios": "^1.7.3", "crypto-js": "^4.2.0", "webrtc-adapter": "^9.0.1"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/preset-env": "^7.25.3", "@rollup/plugin-babel": "^6.0.4", "@rollup/plugin-commonjs": "^26.0.1", "@rollup/plugin-json": "^6.1.0", "@rollup/plugin-node-resolve": "^15.2.3", "@rollup/plugin-terser": "^0.4.4", "@rollup/plugin-typescript": "^11.1.6", "rimraf": "^6.0.1", "rollup": "^4.46.4", "tslib": "^2.6.3"}}