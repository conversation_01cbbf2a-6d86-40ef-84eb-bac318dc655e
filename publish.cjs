const fs = require("fs");
const path = require("path");
const { execSync } = require("child_process");

// 自增版本号，满10进1
const incrementVersion = (version) => {
  const [major, minor, patch] = version.split(".").map(Number);

  let newPatch = patch + 1;
  let newMinor = minor;
  let newMajor = major;

  if (newPatch >= 10) {
    newPatch = 0;
    newMinor += 1;
    if (newMinor >= 10) {
      newMinor = 0;
      newMajor += 1;
    }
  }

  return `${newMajor}.${newMinor}.${newPatch}`;
};

const packageJsonPath = path.resolve(
  __dirname,
  "./packages/armcloud-rtc/package.json"
);

const originalPackageJson = JSON.parse(
  fs.readFileSync(packageJsonPath, "utf-8")
);

let newVersion = incrementVersion(originalPackageJson.version);

const updatedPackageJson = {
  ...originalPackageJson,
  main: "dist/index.cjs.js",
  module: "dist/index.es.js",
  types: "dist/types/index.d.ts",
  scripts: {},
  dependencies: {},
  devDependencies: {},
  version: newVersion,
};
fs.writeFileSync(packageJsonPath, JSON.stringify(updatedPackageJson, null, 2));

// 发布 npm 包
console.log(`正在发布v${newVersion}版本...`);
try {
  execSync("npm publish", {
    cwd: path.resolve(__dirname, "./packages/armcloud-rtc"),
  });
  console.log(`发布v${newVersion}版本成功`);
} catch (error) {
  newVersion = originalPackageJson.version;
  console.error(`发布v${newVersion}版本失败`);
} finally {
  fs.writeFileSync(
    packageJsonPath,
    JSON.stringify({ ...originalPackageJson, version: newVersion }, null, 2)
  );
  process.exit(1);
}
