import typescript from "@rollup/plugin-typescript";
import commonjs from "@rollup/plugin-commonjs";
import { nodeResolve } from "@rollup/plugin-node-resolve";
import terser from "@rollup/plugin-terser";
import json from "@rollup/plugin-json";

export default {
  input: "./src/index.ts",
  output: [
    {
      file: "dist/index.cjs.js",
      format: "cjs",
      sourcemap: true,
    },
    {
      file: "dist/index.es.js",
      format: "es",
      sourcemap: true,
    },
    {
      file: "dist/index.global.js",
      format: "iife",
      name: "ArmcloudRtc",
      sourcemap: true,
    },
  ],
  plugins: [
    nodeResolve({
      preferBuiltins: true,
      browser: true,
    }),
    json(),
    commonjs({
      extensions: [".js"],
      ignore: ["bufferutil", "utf-8-validate"],
      sourceMap: false,
    }),
    terser({
      compress: {
        drop_console: false,
        drop_debugger: true,
      },
    }),
    typescript({
      tsconfig: "./tsconfig.json",
    }),
  ],
};
