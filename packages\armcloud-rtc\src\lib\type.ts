/**
 * 火山云RTC初始化参数
 * @param appId
 * @param roomCode rtc房间code
 * @param roomToken rtc房间token
 * @param clientId 远程云机id
 * @param userId 用户id
 * @param expirationTime 无操作回收时间 秒
 * @param selfAdaption 渲染模式 1：自适应屏幕 2：填充屏幕
 * @param mediaType 媒体流类型，1：只控制音频 2：只控制视频 3：同时控制音频和视频
 * @param rotateType 旋转屏幕 type 1：横屏；2：竖屏
 * @param definition 设置清晰度 1：标清 2：高清 3：超清
 * @param useLocalKeyboard 是否启用本地输入法
 * @param allowCopyRemoteToLocal 允许云机复制到真机
 */
export interface CloudCoreConfig {
  appId: string
  roomCode: string
  roomToken: string
  clientId: string
  userId: string
  expirationTime?: number
  selfAdaption?: string | number
  mediaType?: string | number
  rotateType?: number
  definition?: string | number
  useLocalKeyboard?: boolean
  allowCopyRemoteToLocal?: boolean
}

/**
 * 手指触摸配置参数
 * @action 0 按下 1 抬起 2 触摸中
 * @widthPixels 画布宽度
 * @heightPixels 画布高度
 * @pointCount 操作手指数量
 * @touchType touchType: 'gesture'写死
 * @properties 同时操控手指数组 手指id: 自定义；toolType: 1写死
 * @coords 操作坐标 pressure: 1.0, size: 1.0,写死 x: x坐标；y：y坐标
 */
export interface TouchConfig {
  action: number | string
  widthPixels: number
  heightPixels: number
  pointCount: number
  touchType: string
  properties: PropertiesInfo[]
  coords: CoordsInfo[]
}
interface PropertiesInfo {
  id: number
  toolType: number
}
interface CoordsInfo {
  mPackedAxisValues?: any
  pressure: number
  size: number
  x: number
  y: number
}

/**
 * 自定义清晰度
 * @definitionId 分辨率 7：144*256；8：216*384；9：288*512；10：360*640；11：480*848；12：540*960；13：600*1024；14：480*1280；15：720*1280；16：720*1920；17：1080*1920；18：1440*1920；19：1600*2560；20：2880*1080
 * @framerateId 帧率 1：20fps；2：25fps；3：30fps；4：60fps；5：1fps；6：5fps；7：10fps；8：15fps；9：2fps
 * @bitrateId 码率 1：1Mbps；2：1.5Mbps；3：2Mbps；4：2.5Mbps；5：3Mbps；6：3.5Mbps；7：4Mbps；8：5Mbps；9：6Mbps；10：8Mbps；11：10Mbps；12：12Mbps；13：200kbps；14：400kbps；15：600kbps
 */
export interface CustomDefinition {
  definitionId: number | null
  framerateId: number | null
  bitrateId: number | null
}

/**
 * 定位信息
 * @accuracy 精度
 * @altitude 海拔高度
 * @latitude 纬度
 * @longitude 经度
 * @speed 速度
 * @time 时间
 */
export interface PositionOption {
  accuracy?: number | null
  altitude?: number | null
  latitude: number | null
  longitude: number | null
  speed?: number | null
  time?: number | null
}
