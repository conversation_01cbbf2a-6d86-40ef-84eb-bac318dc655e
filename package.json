{"name": "vmos-js-sdk-main", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "build:sdk": "pnpm --filter armcloud-rtc run build", "publish:sdk": "node ./publish.cjs"}, "dependencies": {"armcloud-rtc": "workspace:^", "axios": "^1.7.3", "vant": "^4.9.4", "vue": "^3.4.35"}, "devDependencies": {"@vitejs/plugin-basic-ssl": "^1.1.0", "@vitejs/plugin-vue": "^5.1.2", "@vitejs/plugin-vue-jsx": "^4.0.0", "autoprefixer": "^10.4.20", "less": "^4.2.0", "postcss": "^8.4.41", "postcss-px-to-viewport-8-plugin": "^1.2.5", "tailwind": "^4.0.0", "tailwindcss": "^3.4.9", "typescript": "^5.5.3", "unplugin-vue-components": "^0.27.3", "vconsole": "^3.15.1", "vite": "^5.4.0", "vite-plugin-node-polyfills": "^0.22.0", "vue-tsc": "^2.0.29"}}